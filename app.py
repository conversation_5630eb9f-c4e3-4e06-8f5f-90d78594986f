from flask import Flask, jsonify, render_template, request
from flask_cors import CORS
import asyncio
import json
import time
from datetime import datetime
import threading
from smart_scanner import (
    init_smart_exchanges, smart_scan, find_smart_arbitrage,
    display_smart_results, cleanup, SMART_EXCHANGES
)

app = Flask(__name__)
CORS(app)

# Global variables to store scanner data
scanner_data = {
    'opportunities': [],
    'last_scan_time': None,
    'scan_in_progress': False,
    'exchange_status': {},
    'total_tokens': 0,
    'successful_requests': 0
}

# Exchange URL mappings for quick links
EXCHANGE_URLS = {
    'binance': 'https://www.binance.com/en/trade/{symbol}',
    'coinbase': 'https://pro.coinbase.com/trade/{symbol}',
    'kraken': 'https://trade.kraken.com/markets/kraken/{symbol}',
    'mexc': 'https://www.mexc.com/exchange/{symbol}',
    'okx': 'https://www.okx.com/trade-spot/{symbol}',
    'kucoin': 'https://trade.kucoin.com/{symbol}',
    'bitmart': 'https://www.bitmart.com/trade/en?symbol={symbol}',
    'lbank': 'https://www.lbank.info/exchange/{symbol}',
    'probit': 'https://www.probit.com/en-us/app/exchange/{symbol}',
    'cryptocom': 'https://crypto.com/exchange/trade/spot/{symbol}',
    'ascendex': 'https://ascendex.com/en/cashtrade-spottrading/{symbol}',
    'bigone': 'https://big.one/en/trade/{symbol}',
    'bitrue': 'https://www.bitrue.com/trade/{symbol}',
    'cex': 'https://cex.io/trade/{symbol}',
    'digifinex': 'https://www.digifinex.com/en-ww/trade/{symbol}',
    'poloniex': 'https://poloniex.com/trade/{symbol}',
    'phemex': 'https://phemex.com/trade/{symbol}',
    'woo': 'https://x.woo.org/en/trade/{symbol}',
    'xt': 'https://www.xt.com/en/trade/{symbol}',
    'latoken': 'https://latoken.com/exchange/{symbol}',
    'novadax': 'https://www.novadax.com.br/trading/{symbol}',
    'tokocrypto': 'https://www.tokocrypto.com/trade/{symbol}',
    'coincatch': 'https://www.coincatch.com/spot/{symbol}',
    'hashkey': 'https://pro.hashkey.com/en-US/spot/{symbol}',
    'oceanex': 'https://oceanex.pro/en/trades/{symbol}',
    'p2b': 'https://p2pb2b.com/trade/{symbol}',
    'tradeogre': 'https://tradeogre.com/exchange/{symbol}',
    'yobit': 'https://yobit.net/en/trade/{symbol}',
    'hollaex': 'https://pro.hollaex.com/trade/{symbol}',
    'btcalpha': 'https://btc-alpha.com/exchange/{symbol}',
    'btcturk': 'https://pro.btcturk.com/pro/al-sat/{symbol}',
    'blockchaincom': 'https://exchange.blockchain.com/trade/{symbol}',
    'bitteam': 'https://bit.team/trade/{symbol}',
    'cryptomus': 'https://cryptomus.com/trading/{symbol}',
    'coinsph': 'https://pro.coins.ph/trade/{symbol}',
    'bitmex': 'https://www.bitmex.com/app/trade/{symbol}',
    'myokx': 'https://www.okx.com/trade-spot/{symbol}'
}

def get_exchange_url(exchange, symbol):
    """Generate exchange URL for a given symbol"""
    if exchange in EXCHANGE_URLS:
        # Convert symbol format (e.g., BTC/USDT -> BTC_USDT, BTCUSDT, or BTC-USDT)
        if exchange in ['binance', 'mexc', 'okx', 'myokx']:
            formatted_symbol = symbol.replace('/', '')
        elif exchange == 'probit':
            formatted_symbol = symbol.replace('/', '-')
        else:
            formatted_symbol = symbol.replace('/', '_')
        return EXCHANGE_URLS[exchange].format(symbol=formatted_symbol)
    return f"https://{exchange}.com"

async def run_scanner():
    """Run the arbitrage scanner asynchronously"""
    global scanner_data

    try:
        scanner_data['scan_in_progress'] = True
        print("🚀 Starting web scanner...")

        # Initialize exchanges
        token_exchange_map = await init_smart_exchanges()

        if not token_exchange_map:
            scanner_data['scan_in_progress'] = False
            return

        scanner_data['total_tokens'] = len(token_exchange_map)

        # Run smart scan with volume validation
        results = await smart_scan(
            token_exchange_map,
            max_concurrent=60,
            volume_validation=True
        )

        scanner_data['successful_requests'] = len(results)

        if results:
            # Find arbitrage opportunities
            opportunities = find_smart_arbitrage(results, volume_validation=True)

            # Format opportunities for web display
            formatted_opportunities = []
            for opp in opportunities:
                # Smart rounding for prices - preserve more decimals for very small values
                def smart_round_price(price):
                    if price == 0:
                        return 0
                    elif price >= 1:
                        return round(price, 6)
                    elif price >= 0.01:
                        return round(price, 8)
                    elif price >= 0.0001:
                        return round(price, 10)
                    elif price >= 0.000000001:  # 1 Gwei and above
                        return round(price, 12)
                    else:
                        # For extremely small prices, preserve up to 18 decimals
                        return round(price, 18)

                formatted_opp = {
                    'symbol': opp['symbol'],
                    'token': opp['symbol'].split('/')[0],
                    'buy_exchange': opp['buy_exchange'],
                    'sell_exchange': opp['sell_exchange'],
                    'buy_price': smart_round_price(opp['buy_price']),
                    'sell_price': smart_round_price(opp['sell_price']),
                    'profit_percentage': round(opp['profit_percentage'], 3),
                    'profit_absolute': smart_round_price(opp['profit_absolute']),
                    'exchanges_count': opp['exchanges_count'],
                    'volume_validated': opp.get('volume_validated', False),
                    'buy_url': get_exchange_url(opp['buy_exchange'], opp['symbol']),
                    'sell_url': get_exchange_url(opp['sell_exchange'], opp['symbol']),
                    'timestamp': time.time()
                }

                # Add volume data if available
                if opp.get('volume_validated'):
                    buy_vol_data = opp.get('buy_volume_data', {})
                    sell_vol_data = opp.get('sell_volume_data', {})

                    buy_vol_usd = buy_vol_data.get('available_volume_usd', 0)
                    sell_vol_usd = sell_vol_data.get('available_volume_usd', 0)
                    buy_slip = buy_vol_data.get('slippage_percent', 0)
                    sell_slip = sell_vol_data.get('slippage_percent', 0)

                    # Calculate max USD amounts that can be bought/sold within 3% slippage
                    buy_price = opp['buy_price']
                    sell_price = opp['sell_price']

                    # Max USD we can spend to buy tokens (this is the available volume)
                    max_buy_usd = buy_vol_usd

                    # Max USD we can get from selling tokens (this is the available volume)
                    max_sell_usd = sell_vol_usd

                    # For profit calculation, we need to convert to tokens first
                    max_buy_tokens = buy_vol_usd / buy_price if buy_price > 0 else 0
                    max_sell_tokens = sell_vol_usd / sell_price if sell_price > 0 else 0

                    # Max profit is limited by the smaller of buy or sell capacity
                    limiting_tokens = min(max_buy_tokens, max_sell_tokens)
                    max_profit_usd = limiting_tokens * (sell_price - buy_price) if limiting_tokens > 0 else 0

                    # Get orderbook data for dynamic slippage calculation
                    buy_orderbook = None
                    sell_orderbook = None

                    # Extract orderbook data from the scanner results
                    for result in results:
                        if (result['symbol'] == opp['symbol'] and
                            result['exchange'] == opp['buy_exchange'] and
                            result.get('orderbook')):
                            buy_orderbook = result['orderbook']
                        elif (result['symbol'] == opp['symbol'] and
                              result['exchange'] == opp['sell_exchange'] and
                              result.get('orderbook')):
                            sell_orderbook = result['orderbook']

                    formatted_opp.update({
                        'min_volume': round(min(buy_vol_usd, sell_vol_usd), 2),
                        'max_slippage': round(max(buy_slip, sell_slip), 3),
                        'max_buy_usd': round(max_buy_usd, 2),
                        'max_sell_usd': round(max_sell_usd, 2),
                        'max_profit_usd': round(max_profit_usd, 4),
                        'buy_orderbook': buy_orderbook,
                        'sell_orderbook': sell_orderbook
                    })

                formatted_opportunities.append(formatted_opp)

            scanner_data['opportunities'] = formatted_opportunities
            scanner_data['last_scan_time'] = datetime.now().isoformat()

            print(f"✅ Scanner completed: {len(opportunities)} opportunities found")

        await cleanup()

    except Exception as e:
        print(f"❌ Scanner error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        scanner_data['scan_in_progress'] = False

def run_scanner_thread():
    """Run scanner in a separate thread"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    loop.run_until_complete(run_scanner())
    loop.close()

@app.route('/')
def index():
    """Serve the main web interface"""
    return render_template('index.html')

@app.route('/api/opportunities')
def get_opportunities():
    """API endpoint to get current arbitrage opportunities"""
    return jsonify({
        'opportunities': scanner_data['opportunities'],
        'last_scan_time': scanner_data['last_scan_time'],
        'scan_in_progress': scanner_data['scan_in_progress'],
        'total_tokens': scanner_data['total_tokens'],
        'successful_requests': scanner_data['successful_requests'],
        'total_opportunities': len(scanner_data['opportunities'])
    })

@app.route('/api/scan', methods=['POST'])
def trigger_scan():
    """API endpoint to trigger a new scan"""
    if scanner_data['scan_in_progress']:
        return jsonify({'error': 'Scan already in progress'}), 400

    # Start scanner in background thread
    thread = threading.Thread(target=run_scanner_thread)
    thread.daemon = True
    thread.start()

    return jsonify({'message': 'Scan started'})

@app.route('/api/status')
def get_status():
    """API endpoint to get scanner status"""
    return jsonify({
        'scan_in_progress': scanner_data['scan_in_progress'],
        'last_scan_time': scanner_data['last_scan_time'],
        'total_opportunities': len(scanner_data['opportunities']),
        'exchanges_available': len(SMART_EXCHANGES)
    })

@app.route('/api/recalculate-slippage', methods=['POST'])
def recalculate_slippage():
    """API endpoint to recalculate volumes with custom slippage values"""
    try:
        data = request.get_json()
        buy_slippage = float(data.get('buy_slippage', 3.0))
        sell_slippage = float(data.get('sell_slippage', 3.0))

        # Import the validation function
        from smart_scanner import validate_orderbook_volume

        # Recalculate volumes for all opportunities
        updated_opportunities = []
        for opp in scanner_data['opportunities']:
            updated_opp = opp.copy()

            if (opp.get('volume_validated') and
                opp.get('buy_orderbook') and
                opp.get('sell_orderbook')):

                # Recalculate buy side with new slippage
                buy_validation = validate_orderbook_volume(
                    opp['buy_orderbook'],
                    'ask',  # We're buying, so we look at asks
                    min_volume_usd=10.0,
                    max_slippage_percent=buy_slippage
                )

                # Recalculate sell side with new slippage
                sell_validation = validate_orderbook_volume(
                    opp['sell_orderbook'],
                    'bid',  # We're selling, so we look at bids
                    min_volume_usd=10.0,
                    max_slippage_percent=sell_slippage
                )

                if buy_validation['valid'] and sell_validation['valid']:
                    # Update volumes with new calculations
                    max_buy_usd = buy_validation['available_volume_usd']
                    max_sell_usd = sell_validation['available_volume_usd']

                    # Calculate max profit with new volumes
                    buy_price = opp['buy_price']
                    sell_price = opp['sell_price']
                    max_buy_tokens = max_buy_usd / buy_price if buy_price > 0 else 0
                    max_sell_tokens = max_sell_usd / sell_price if sell_price > 0 else 0
                    limiting_tokens = min(max_buy_tokens, max_sell_tokens)
                    max_profit_usd = limiting_tokens * (sell_price - buy_price) if limiting_tokens > 0 else 0

                    updated_opp.update({
                        'max_buy_usd': round(max_buy_usd, 2),
                        'max_sell_usd': round(max_sell_usd, 2),
                        'max_profit_usd': round(max_profit_usd, 4),
                        'min_volume': round(min(max_buy_usd, max_sell_usd), 2),
                        'current_buy_slippage': buy_slippage,
                        'current_sell_slippage': sell_slippage,
                        'buy_slippage_actual': round(buy_validation['slippage_percent'], 3),
                        'sell_slippage_actual': round(sell_validation['slippage_percent'], 3)
                    })
                else:
                    # If validation fails, mark as invalid
                    updated_opp.update({
                        'max_buy_usd': 0,
                        'max_sell_usd': 0,
                        'max_profit_usd': 0,
                        'min_volume': 0,
                        'current_buy_slippage': buy_slippage,
                        'current_sell_slippage': sell_slippage,
                        'volume_validated': False,
                        'slippage_error': 'Insufficient volume at requested slippage'
                    })

            updated_opportunities.append(updated_opp)

        return jsonify({
            'success': True,
            'opportunities': updated_opportunities,
            'buy_slippage': buy_slippage,
            'sell_slippage': sell_slippage
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 400

if __name__ == '__main__':
    print("🌐 Starting Arbitrage Scanner Web Interface...")
    print("📊 Access the dashboard at: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
