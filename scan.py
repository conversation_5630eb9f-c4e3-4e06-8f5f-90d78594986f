import requests
import ccxt
import ccxt.async_support as ccxt_async
from collections import defaultdict
import time
import asyncio
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

EXCHANGES = [
    "ascendex",
    "bigone",
    "binance",
    "bitmart",
    "bitmex",
    "bitrue",
    "bitteam",
    "blockchaincom",
    "btcalpha",
    "btcturk",
    "cex",
    "coinbase",
    "coincatch",
    "coinsph",
    "cryptocom",
    "cryptomus",
    "digifinex",
    "hashkey",
    "hollaex",
    "kraken",
    "latoken",
    "lbank",
    "mexc",
    "myokx",
    "novadax",
    "oceanex",
    "okx",
    "p2b",
    "phemex",
    "poloniex",
    "probit",
    "tokocrypto",
    "tradeogre",
    "woo",
    "xt",
    "yobit"
]


def scan_single_exchange(exchange_name):
    """
    Scan a single exchange for USDT pairs and return the results
    """
    try:
        print(f"Starting scan of {exchange_name}...")

        current_exchange = getattr(ccxt, exchange_name)()

        # Get the list of all available symbols
        markets = current_exchange.load_markets()
        usdt_symbols = [symbol for symbol in markets if symbol.endswith('/USDT')]

        # Extract base tokens (the part before /USDT)
        base_tokens = set()
        for symbol in usdt_symbols:
            base_token = symbol.split('/')[0]
            base_tokens.add(base_token)

        print(f"✓ {exchange_name}: Found {len(usdt_symbols)} USDT pairs ({len(base_tokens)} unique tokens)")

        return exchange_name, base_tokens, None

    except Exception as e:
        print(f"✗ {exchange_name}: Error - {e}")
        return exchange_name, set(), str(e)

def get_tokens_on_multiple_exchanges(min_exchanges=2, max_workers=len(EXCHANGES)):
    """
    Get a list of all tokens that appear on at least min_exchanges exchanges
    Processes all exchanges simultaneously using concurrent requests

    Args:
        min_exchanges (int): Minimum number of exchanges a token must appear on
        max_workers (int): Maximum number of concurrent threads to use
    """
    # Dictionary to count how many exchanges each token appears on
    token_exchange_count = defaultdict(set)

    print(f"Scanning {len(EXCHANGES)} exchanges for tokens simultaneously...")
    print("=" * 60)

    start_time = time.time()
    successful_scans = 0
    failed_scans = 0

    # Process all exchanges simultaneously using ThreadPoolExecutor
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all exchange scan tasks
        future_to_exchange = {
            executor.submit(scan_single_exchange, exchange_name): exchange_name
            for exchange_name in EXCHANGES
        }

        # Process completed tasks as they finish
        for future in as_completed(future_to_exchange):
            exchange_name, base_tokens, error = future.result()

            if error is None:
                # Add tokens to our count
                for token in base_tokens:
                    token_exchange_count[token].add(exchange_name)
                successful_scans += 1
            else:
                failed_scans += 1

    end_time = time.time()
    scan_duration = end_time - start_time

    print("=" * 60)
    print(f"Scan completed in {scan_duration:.2f} seconds")
    print(f"Successful scans: {successful_scans}/{len(EXCHANGES)}")
    print(f"Failed scans: {failed_scans}/{len(EXCHANGES)}")

    # Filter tokens that appear on at least min_exchanges exchanges
    multi_exchange_tokens = {}
    for token, exchanges in token_exchange_count.items():
        if len(exchanges) >= min_exchanges:
            multi_exchange_tokens[token] = list(exchanges)

    return multi_exchange_tokens


async def fetch_orderbook_async(exchange_name, symbol, semaphore):
    """
    Fetch orderbook for a single symbol from a single exchange using async
    """
    async with semaphore:  # Limit concurrent requests
        try:
            # Create async exchange instance
            exchange_class = getattr(ccxt_async, exchange_name)
            exchange = exchange_class({
                'enableRateLimit': True,
                'timeout': 10000,  # 10 second timeout
            })

            # Fetch orderbook
            orderbook = await exchange.fetch_order_book(symbol, limit=10)
            await exchange.close()  # Important: close the session

            return {
                'exchange': exchange_name,
                'symbol': symbol,
                'orderbook': orderbook,
                'timestamp': time.time(),
                'error': None
            }

        except Exception as e:
            return {
                'exchange': exchange_name,
                'symbol': symbol,
                'orderbook': None,
                'timestamp': time.time(),
                'error': str(e)
            }


async def batch_fetch_orderbooks(tokens_exchanges_dict, max_concurrent=10):
    """
    Batch fetch orderbooks for multiple tokens across multiple exchanges

    Args:
        tokens_exchanges_dict: Dict with token as key and list of exchanges as value
        max_concurrent: Maximum number of concurrent requests

    Returns:
        List of orderbook results
    """
    print(f"🔄 Starting batch orderbook fetch with max {max_concurrent} concurrent requests...")

    # Create semaphore to limit concurrent requests
    semaphore = asyncio.Semaphore(max_concurrent)

    # Create tasks for all token-exchange combinations
    tasks = []
    total_requests = 0

    for token, exchanges in tokens_exchanges_dict.items():
        symbol = f"{token}/USDT"
        for exchange_name in exchanges:
            task = fetch_orderbook_async(exchange_name, symbol, semaphore)
            tasks.append(task)
            total_requests += 1

    print(f"📊 Total requests to make: {total_requests}")
    print(f"📈 Tokens to fetch: {len(tokens_exchanges_dict)}")

    start_time = time.time()

    # Execute all tasks concurrently with progress reporting
    print("🔄 Progress: ", end="", flush=True)
    completed = 0
    successful_results = []
    failed_results = []

    # Process results as they complete for progress reporting
    for coro in asyncio.as_completed(tasks):
        try:
            result = await coro
            completed += 1

            if result['error'] is None:
                successful_results.append(result)
            else:
                failed_results.append(result)

            # Progress indicator
            if completed % max(1, total_requests // 20) == 0:  # Update every 5%
                progress = (completed / total_requests) * 100
                print(f"{progress:.0f}%", end=" ", flush=True)

        except Exception as e:
            completed += 1
            failed_results.append(str(e))

    print()  # New line after progress

    end_time = time.time()
    duration = end_time - start_time

    print(f"✅ Batch fetch completed in {duration:.2f} seconds ({duration/60:.1f} minutes)")
    print(f"📊 Successful: {len(successful_results)}/{total_requests}")
    print(f"❌ Failed: {len(failed_results)}/{total_requests}")
    print(f"⚡ Average speed: {total_requests/duration:.1f} requests/second")

    return successful_results, failed_results


def analyze_arbitrage_opportunities(orderbook_results):
    """
    Analyze orderbook results for potential arbitrage opportunities
    """
    print("\n🔍 Analyzing arbitrage opportunities...")

    # Group results by symbol
    symbol_data = defaultdict(list)
    for result in orderbook_results:
        symbol_data[result['symbol']].append(result)

    opportunities = []

    for symbol, exchange_data in symbol_data.items():
        if len(exchange_data) < 2:
            continue  # Need at least 2 exchanges for arbitrage

        # Find best bid and ask across exchanges
        best_bid = None
        best_ask = None
        best_bid_exchange = None
        best_ask_exchange = None

        for data in exchange_data:
            orderbook = data['orderbook']
            if not orderbook or not orderbook.get('bids') or not orderbook.get('asks'):
                continue

            # Get best bid and ask
            if orderbook['bids']:
                bid_price = orderbook['bids'][0][0]  # [price, amount]
                if best_bid is None or bid_price > best_bid:
                    best_bid = bid_price
                    best_bid_exchange = data['exchange']

            if orderbook['asks']:
                ask_price = orderbook['asks'][0][0]  # [price, amount]
                if best_ask is None or ask_price < best_ask:
                    best_ask = ask_price
                    best_ask_exchange = data['exchange']

        # Calculate potential profit
        if best_bid and best_ask and best_bid > best_ask:
            profit_percentage = ((best_bid - best_ask) / best_ask) * 100

            opportunities.append({
                'symbol': symbol,
                'buy_exchange': best_ask_exchange,
                'sell_exchange': best_bid_exchange,
                'buy_price': best_ask,
                'sell_price': best_bid,
                'profit_percentage': profit_percentage,
                'profit_absolute': best_bid - best_ask
            })

    # Sort by profit percentage
    opportunities.sort(key=lambda x: x['profit_percentage'], reverse=True)

    return opportunities


def display_arbitrage_opportunities(opportunities, top_n=10):
    """
    Display top arbitrage opportunities
    """
    print(f"\n🎯 Top {min(top_n, len(opportunities))} Arbitrage Opportunities:")
    print("=" * 80)

    if not opportunities:
        print("No arbitrage opportunities found.")
        return

    for i, opp in enumerate(opportunities[:top_n], 1):
        print(f"{i}. {opp['symbol']}")
        print(f"   Buy on {opp['buy_exchange']} at ${opp['buy_price']:.6f}")
        print(f"   Sell on {opp['sell_exchange']} at ${opp['sell_price']:.6f}")
        print(f"   Profit: {opp['profit_percentage']:.3f}% (${opp['profit_absolute']:.6f})")
        print("-" * 40)


if __name__ == "__main__":
    # Configuration
    MIN_EXCHANGES = 2
    MAX_CONCURRENT_REQUESTS = 8  # Reduced for stability with large dataset

    print("🚀 Multi-Exchange Token Scanner with FULL Orderbook Analysis")
    print(f"Configuration: min_exchanges={MIN_EXCHANGES}")
    print(f"Max concurrent requests: {MAX_CONCURRENT_REQUESTS}")
    print()

    # Get tokens that appear on at least MIN_EXCHANGES exchanges
    multi_exchange_tokens = get_tokens_on_multiple_exchanges(
        min_exchanges=MIN_EXCHANGES
    )

    print(f"\n📋 Found {len(multi_exchange_tokens)} tokens on multiple exchanges")

    # Calculate total requests
    total_requests = sum(len(exchanges) for exchanges in multi_exchange_tokens.values())
    print(f"📊 Total orderbook requests to make: {total_requests}")

    # Estimate time
    estimated_time = (total_requests / MAX_CONCURRENT_REQUESTS) * 2  # ~2 seconds per batch
    print(f"⏱️  Estimated completion time: {estimated_time/60:.1f} minutes")

    # Ask for confirmation for large datasets
    if total_requests > 1000:
        print(f"\n⚠️  This will make {total_requests} API requests!")
        print("💡 Consider reducing MIN_EXCHANGES or MAX_CONCURRENT_REQUESTS for faster testing")
        response = input("Continue with full scan? (y/N): ").strip().lower()
        if response != 'y':
            print("Scan cancelled. Try with fewer tokens first.")
            exit()

    print(f"\n🎯 Fetching orderbooks for ALL {len(multi_exchange_tokens)} tokens...")
    print("🔄 This may take several minutes for the full dataset...")

    # Fetch orderbooks asynchronously
    try:
        successful_results, failed_results = asyncio.run(
            batch_fetch_orderbooks(multi_exchange_tokens, MAX_CONCURRENT_REQUESTS)
        )

        if successful_results:
            print(f"\n🎉 Successfully fetched {len(successful_results)} orderbooks!")

            # Analyze arbitrage opportunities
            opportunities = analyze_arbitrage_opportunities(successful_results)
            display_arbitrage_opportunities(opportunities, top_n=20)  # Show top 20

            # Additional statistics
            print(f"\n📈 Final Statistics:")
            print(f"   Total tokens analyzed: {len(multi_exchange_tokens)}")
            print(f"   Successful orderbook fetches: {len(successful_results)}")
            print(f"   Failed requests: {len(failed_results)}")
            print(f"   Success rate: {len(successful_results)/(len(successful_results)+len(failed_results))*100:.1f}%")
            print(f"   Arbitrage opportunities found: {len(opportunities)}")

            if opportunities:
                avg_profit = sum(opp['profit_percentage'] for opp in opportunities) / len(opportunities)
                max_profit = max(opp['profit_percentage'] for opp in opportunities)
                print(f"   Average profit opportunity: {avg_profit:.3f}%")
                print(f"   Maximum profit opportunity: {max_profit:.3f}%")
        else:
            print("❌ No successful orderbook fetches. Check your network connection and exchange availability.")

    except KeyboardInterrupt:
        print("\n⚠️  Scan interrupted by user")
    except Exception as e:
        print(f"❌ Error during async execution: {e}")
        print("💡 Tip: Some exchanges might be down or have strict rate limits.")

