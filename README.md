# Arbitrage Scanner Web Dashboard

A modern web-based interface for cryptocurrency arbitrage scanning across multiple exchanges.

## Features

### 🎯 **Smart Arbitrage Detection**
- Real-time scanning across 35+ cryptocurrency exchanges
- Volume validation with slippage protection ($10 USD minimum, 3% max slippage)
- Smart token-exchange mapping for optimized API requests
- Automatic retry mechanism for failed requests

### 🌐 **Web Interface**
- **Clean Dashboard**: Modern, responsive design that works on desktop and mobile
- **Real-time Data**: Auto-refreshing opportunities every 30 seconds
- **Interactive Filtering**: Search by token, filter by exchange or profit level
- **Sorting Options**: Sort by profit percentage, volume, or symbol
- **Quick Exchange Links**: Direct links to buy/sell pages on each exchange

### 📊 **Data Visualization**
- Color-coded profit levels (High: >5%, Medium: 1-5%, Low: <1%)
- Volume and slippage information for validated opportunities
- Exchange performance statistics
- Real-time status indicators

## Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Start the Web Server
```bash
python app.py
```

### 3. Access the Dashboard
Open your browser and navigate to: `http://localhost:5000`

### 4. Start Scanning
Click the "Start Scan" button to begin scanning for arbitrage opportunities.

## How It Works

### Backend Integration
The web interface integrates with the existing `smart_scanner.py` functionality:

- **Flask API Server**: Provides REST endpoints for the frontend
- **Async Scanner**: Runs the arbitrage scanner in background threads
- **Real-time Updates**: Polls scanner status and updates the UI automatically

### API Endpoints

- `GET /` - Main web interface
- `GET /api/opportunities` - Get current arbitrage opportunities
- `POST /api/scan` - Trigger a new scan
- `GET /api/status` - Get scanner status

### Exchange Support

The scanner supports 35+ exchanges including:
- Binance, Coinbase, Kraken, OKX
- MEXC, LBank, Probit, Bitrue
- Poloniex, Phemex, BigOne, Ascendex
- And many more...

## Interface Features

### Status Bar
- **Status**: Current scanner state (Ready/Scanning/Error)
- **Last Scan**: Timestamp of the most recent scan
- **Opportunities**: Total number of arbitrage opportunities found
- **Exchanges**: Number of exchanges currently available

### Controls
- **Search**: Find specific tokens by symbol
- **Exchange Filter**: Show opportunities involving specific exchanges
- **Profit Filter**: Filter by profit level (High/Medium/Low)
- **Sort Options**: Sort by profit, volume, or alphabetically

### Opportunities Table
Each opportunity shows:
- **Token Symbol**: The cryptocurrency being traded
- **Buy Exchange**: Where to buy at the lower price
- **Sell Exchange**: Where to sell at the higher price
- **Prices**: Exact buy and sell prices
- **Profit**: Both percentage and absolute profit
- **Volume**: Available volume and slippage information
- **Action Buttons**: Direct links to exchange trading pages

### Volume Validation
When enabled, the scanner validates that each opportunity has:
- Minimum $10 USD trading volume available
- Maximum 3% price slippage when executing the trade
- Real orderbook depth analysis

## Technical Details

### Architecture
- **Frontend**: HTML5, CSS3, Vanilla JavaScript
- **Backend**: Flask (Python)
- **Scanner**: Async Python with CCXT library
- **Styling**: Modern CSS with gradients and animations
- **Icons**: Font Awesome for UI elements

### Performance
- **Smart Requests**: Only queries tokens that exist on each exchange
- **Concurrent Processing**: Up to 60 concurrent API requests
- **Retry Logic**: Automatic retry for failed requests
- **Rate Limiting**: Respects exchange rate limits

### Security
- **CORS Enabled**: Allows cross-origin requests
- **Input Validation**: Sanitizes user inputs
- **Error Handling**: Graceful error handling and user feedback

## Customization

### Exchange URLs
The `EXCHANGE_URLS` dictionary in `app.py` can be modified to update exchange trading page URLs.

### Styling
Modify `static/style.css` to customize the appearance:
- Color schemes
- Layout and spacing
- Responsive breakpoints
- Animation effects

### Scanner Settings
Adjust scanner parameters in `smart_scanner.py`:
- Volume requirements
- Slippage tolerance
- Concurrent request limits
- Retry strategies

## Troubleshooting

### Common Issues

1. **Scanner Not Starting**: Check that all dependencies are installed
2. **No Opportunities Found**: Some exchanges may be temporarily unavailable
3. **Slow Performance**: Reduce concurrent request limits in scanner settings
4. **Exchange Errors**: Some exchanges require API keys or have regional restrictions

### Logs
Check the console output for detailed scanner logs and error messages.

## Development

### Adding New Exchanges
1. Add exchange name to `SMART_EXCHANGES` list in `smart_scanner.py`
2. Add exchange URL pattern to `EXCHANGE_URLS` in `app.py`
3. Test the exchange integration

### Extending the API
Add new endpoints in `app.py` following the existing pattern:
```python
@app.route('/api/new-endpoint')
def new_endpoint():
    return jsonify({'data': 'value'})
```

## License

This project is for educational and research purposes. Always verify prices on exchanges before trading.

## Disclaimer

Cryptocurrency trading involves significant risk. This tool is for informational purposes only and should not be considered financial advice. Always verify opportunities manually before executing trades.
