// Global variables
let opportunities = [];
let filteredOpportunities = [];
let scanInterval = null;

// Local storage keys
const STORAGE_KEYS = {
    OPPORTUNITIES: 'arbitrage_opportunities',
    LAST_SCAN_TIME: 'arbitrage_last_scan_time',
    SCAN_STATUS: 'arbitrage_scan_status'
};

// DOM elements
const scanBtn = document.getElementById('scanBtn');
const refreshBtn = document.getElementById('refreshBtn');
const searchInput = document.getElementById('searchInput');
const exchangeFilter = document.getElementById('exchangeFilter');
const profitFilter = document.getElementById('profitFilter');
const sortBy = document.getElementById('sortBy');
const buySlippageSlider = document.getElementById('buySlippageSlider');
const sellSlippageSlider = document.getElementById('sellSlippageSlider');
const buySlippageValue = document.getElementById('buySlippageValue');
const sellSlippageValue = document.getElementById('sellSlippageValue');
const resetSlippage = document.getElementById('resetSlippage');
const loadingIndicator = document.getElementById('loadingIndicator');
const opportunitiesTable = document.getElementById('opportunitiesTable');
const opportunitiesBody = document.getElementById('opportunitiesBody');
const emptyState = document.getElementById('emptyState');

// Status elements
const scanStatus = document.getElementById('scanStatus');
const lastScan = document.getElementById('lastScan');
const opportunityCount = document.getElementById('opportunityCount');
const exchangeCount = document.getElementById('exchangeCount');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
    loadStoredData();
    loadOpportunities();
    startAutoRefresh();
});

function setupEventListeners() {
    scanBtn.addEventListener('click', startScan);
    refreshBtn.addEventListener('click', function() {
        clearStoredData();
        loadOpportunities();
    });
    searchInput.addEventListener('input', applyFilters);
    exchangeFilter.addEventListener('change', applyFilters);
    profitFilter.addEventListener('change', applyFilters);
    sortBy.addEventListener('change', applyFilters);
    buySlippageSlider.addEventListener('input', function() {
        buySlippageValue.textContent = parseFloat(buySlippageSlider.value).toFixed(1);
        recalculateVolumesWithSlippage();
    });
    sellSlippageSlider.addEventListener('input', function() {
        sellSlippageValue.textContent = parseFloat(sellSlippageSlider.value).toFixed(1);
        recalculateVolumesWithSlippage();
    });
    resetSlippage.addEventListener('click', function() {
        buySlippageSlider.value = 3.0;
        sellSlippageSlider.value = 3.0;
        buySlippageValue.textContent = '3.0';
        sellSlippageValue.textContent = '3.0';
        recalculateVolumesWithSlippage();
    });
}

// Local storage functions
function saveToStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
    } catch (error) {
        console.warn('Failed to save to localStorage:', error);
    }
}

function loadFromStorage(key) {
    try {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : null;
    } catch (error) {
        console.warn('Failed to load from localStorage:', error);
        return null;
    }
}

function clearStoredData() {
    try {
        localStorage.removeItem(STORAGE_KEYS.OPPORTUNITIES);
        localStorage.removeItem(STORAGE_KEYS.LAST_SCAN_TIME);
        localStorage.removeItem(STORAGE_KEYS.SCAN_STATUS);
    } catch (error) {
        console.warn('Failed to clear localStorage:', error);
    }
}

function loadStoredData() {
    // Load stored opportunities if available
    const storedOpportunities = loadFromStorage(STORAGE_KEYS.OPPORTUNITIES);
    const storedLastScan = loadFromStorage(STORAGE_KEYS.LAST_SCAN_TIME);

    if (storedOpportunities && storedOpportunities.length > 0) {
        opportunities = storedOpportunities;
        populateFilters();
        applyFilters();

        // Update status bar with stored data
        opportunityCount.textContent = opportunities.length;

        if (storedLastScan) {
            const scanTime = new Date(storedLastScan);
            lastScan.textContent = scanTime.toLocaleString() + ' (cached)';
        }

        updateStatus('Ready (cached data)', 'text-info');
        console.log('Loaded', opportunities.length, 'opportunities from cache');
    }
}

function startAutoRefresh() {
    // Check for updates every 30 seconds, but only load if no cached data or scan is in progress
    scanInterval = setInterval(async () => {
        try {
            const response = await fetch('/api/status');
            const status = await response.json();

            // Only auto-refresh if scan is in progress or we have no cached data
            if (status.scan_in_progress || opportunities.length === 0) {
                loadOpportunities();
            }
        } catch (error) {
            console.warn('Auto-refresh status check failed:', error);
        }
    }, 30000);
}

async function startScan() {
    try {
        // Clear stored data when starting a new scan
        clearStoredData();

        scanBtn.disabled = true;
        scanBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Scanning...';

        const response = await fetch('/api/scan', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            showLoading(true);
            updateStatus('Scanning...', 'text-warning');

            // Poll for scan completion
            pollScanStatus();
        } else {
            const error = await response.json();
            showNotification('Error starting scan: ' + error.error, 'error');
        }
    } catch (error) {
        console.error('Error starting scan:', error);
        showNotification('Error starting scan', 'error');
    } finally {
        scanBtn.disabled = false;
        scanBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Start Scan';
    }
}

async function pollScanStatus() {
    try {
        const response = await fetch('/api/status');
        const status = await response.json();

        if (status.scan_in_progress) {
            setTimeout(pollScanStatus, 2000); // Check again in 2 seconds
        } else {
            showLoading(false);
            loadOpportunities();
            updateStatus('Ready', 'text-success');
        }
    } catch (error) {
        console.error('Error polling scan status:', error);
        showLoading(false);
        updateStatus('Error', 'text-danger');
    }
}

async function loadOpportunities() {
    try {
        const response = await fetch('/api/opportunities');
        const data = await response.json();

        opportunities = data.opportunities || [];

        // Save to localStorage if we have new data
        if (opportunities.length > 0) {
            saveToStorage(STORAGE_KEYS.OPPORTUNITIES, opportunities);
            if (data.last_scan_time) {
                saveToStorage(STORAGE_KEYS.LAST_SCAN_TIME, data.last_scan_time);
            }
        }

        updateStatusBar(data);
        populateFilters();
        applyFilters();

    } catch (error) {
        console.error('Error loading opportunities:', error);
        showNotification('Error loading data', 'error');
    }
}

function updateStatusBar(data) {
    // Update scan status
    if (data.scan_in_progress) {
        updateStatus('Scanning...', 'text-warning');
        showLoading(true);
    } else {
        updateStatus('Ready', 'text-success');
        showLoading(false);
    }

    // Update last scan time
    if (data.last_scan_time) {
        const scanTime = new Date(data.last_scan_time);
        lastScan.textContent = scanTime.toLocaleString();
    }

    // Update counts
    opportunityCount.textContent = data.total_opportunities || 0;
    exchangeCount.textContent = data.exchanges_available || 0;
}

function updateStatus(text, className) {
    scanStatus.textContent = text;
    scanStatus.className = 'status-value ' + className;
}

function populateFilters() {
    // Populate exchange filter
    const exchanges = [...new Set(opportunities.flatMap(opp => [opp.buy_exchange, opp.sell_exchange]))];
    exchangeFilter.innerHTML = '<option value="">All Exchanges</option>';
    exchanges.sort().forEach(exchange => {
        const option = document.createElement('option');
        option.value = exchange;
        option.textContent = exchange.charAt(0).toUpperCase() + exchange.slice(1);
        exchangeFilter.appendChild(option);
    });
}

function applyFilters() {
    const searchTerm = searchInput.value.toLowerCase();
    const selectedExchange = exchangeFilter.value;
    const selectedProfit = profitFilter.value;
    const selectedSort = sortBy.value;

    // Filter opportunities
    filteredOpportunities = opportunities.filter(opp => {
        // Search filter
        if (searchTerm && !opp.token.toLowerCase().includes(searchTerm)) {
            return false;
        }

        // Exchange filter
        if (selectedExchange &&
            opp.buy_exchange !== selectedExchange &&
            opp.sell_exchange !== selectedExchange) {
            return false;
        }

        // Profit filter
        if (selectedProfit) {
            const profit = opp.profit_percentage;
            if (selectedProfit === 'high' && profit <= 5) return false;
            if (selectedProfit === 'medium' && (profit < 1 || profit > 5)) return false;
            if (selectedProfit === 'low' && profit >= 1) return false;
        }

        return true;
    });

    // Sort opportunities
    filteredOpportunities.sort((a, b) => {
        switch (selectedSort) {
            case 'profit_desc':
                return b.profit_percentage - a.profit_percentage;
            case 'profit_asc':
                return a.profit_percentage - b.profit_percentage;
            case 'symbol_asc':
                return a.token.localeCompare(b.token);
            case 'volume_desc':
                return (b.min_volume || 0) - (a.min_volume || 0);
            case 'max_profit_desc':
                return (b.max_profit_usd || 0) - (a.max_profit_usd || 0);
            case 'max_buy_desc':
                return (b.max_buy_usd || 0) - (a.max_buy_usd || 0);
            case 'max_sell_desc':
                return (b.max_sell_usd || 0) - (a.max_sell_usd || 0);
            default:
                return b.profit_percentage - a.profit_percentage;
        }
    });

    renderOpportunities();
}

function renderOpportunities() {
    if (filteredOpportunities.length === 0) {
        opportunitiesTable.style.display = 'none';
        emptyState.style.display = 'block';
        return;
    }

    opportunitiesTable.style.display = 'table';
    emptyState.style.display = 'none';

    opportunitiesBody.innerHTML = '';

    filteredOpportunities.forEach(opp => {
        const row = createOpportunityRow(opp);
        opportunitiesBody.appendChild(row);
    });
}

function createOpportunityRow(opp) {
    const row = document.createElement('tr');

    // Determine profit class
    let profitClass = 'profit-low';
    if (opp.profit_percentage > 5) profitClass = 'profit-high';
    else if (opp.profit_percentage >= 1) profitClass = 'profit-medium';

    // Format prices with appropriate styling
    const buyPriceFormatted = formatPriceWithClass(opp.buy_price);
    const sellPriceFormatted = formatPriceWithClass(opp.sell_price);

    // Add slippage indicator class if either buy or sell slippage has been changed
    const buySlippageChanged = opp.current_buy_slippage && opp.current_buy_slippage !== 3.0;
    const sellSlippageChanged = opp.current_sell_slippage && opp.current_sell_slippage !== 3.0;
    const slippageClass = (buySlippageChanged || sellSlippageChanged) ? 'slippage-modified' : '';

    row.innerHTML = `
        <td><strong>${opp.token}</strong></td>
        <td><a href="${opp.buy_url}" target="_blank" class="exchange-link">${formatExchangeName(opp.buy_exchange)}</a></td>
        <td><span class="${buyPriceFormatted.cssClass}">$${buyPriceFormatted.formatted}</span></td>
        <td><a href="${opp.sell_url}" target="_blank" class="exchange-link">${formatExchangeName(opp.sell_exchange)}</a></td>
        <td><span class="${sellPriceFormatted.cssClass}">$${sellPriceFormatted.formatted}</span></td>
        <td><span class="${profitClass}">+${opp.profit_percentage.toFixed(3)}%</span></td>
        <td>${formatVolume(opp)}</td>
        <td class="${slippageClass}">${formatMaxBuy(opp)}</td>
        <td class="${slippageClass}">${formatMaxSell(opp)}</td>
        <td class="${slippageClass}">${formatMaxProfit(opp)}</td>
        <td>
            <div class="action-buttons">
                <a href="${opp.buy_url}" target="_blank" class="btn-small btn-buy">
                    <i class="fas fa-shopping-cart"></i> Buy
                </a>
                <a href="${opp.sell_url}" target="_blank" class="btn-small btn-sell">
                    <i class="fas fa-hand-holding-usd"></i> Sell
                </a>
            </div>
        </td>
    `;

    return row;
}

function formatExchangeName(exchange) {
    return exchange.charAt(0).toUpperCase() + exchange.slice(1);
}

function formatPrice(price) {
    // Smart formatting for prices - show more decimals for smaller values
    if (price === 0) {
        return '0';
    } else if (price >= 1) {
        return price.toFixed(6);
    } else if (price >= 0.01) {
        return price.toFixed(8);
    } else if (price >= 0.0001) {
        return price.toFixed(10);
    } else if (price >= 0.000000001) {  // 1 Gwei and above
        let formatted = price.toFixed(12);
        // Remove trailing zeros after decimal point
        formatted = formatted.replace(/\.?0+$/, '');
        return formatted;
    } else if (price > 0) {
        // For extremely small prices, try up to 18 decimals
        let formatted = price.toFixed(18);
        // Remove trailing zeros after decimal point
        formatted = formatted.replace(/\.?0+$/, '');
        // If still shows as 0, use scientific notation
        if (formatted === '0' || formatted === '') {
            formatted = price.toExponential(6);
        }
        return formatted;
    } else {
        return '0';
    }
}

function formatPriceWithClass(price) {
    // Format price and return object with formatted value and CSS class
    const formatted = formatPrice(price);
    let cssClass = '';

    // Only apply scientific notation styling
    if (formatted.includes('e')) {
        cssClass = 'price-scientific';
    }

    return {
        formatted: formatted,
        cssClass: cssClass.trim()
    };
}

function formatVolume(opp) {
    if (opp.volume_validated && opp.min_volume) {
        const buySlippage = opp.current_buy_slippage || 3.0;
        const sellSlippage = opp.current_sell_slippage || 3.0;

        if (buySlippage === sellSlippage) {
            return `<span class="volume-amount">$${opp.min_volume.toFixed(0)}</span> <small class="slippage-info">(${buySlippage.toFixed(1)}% slip)</small>`;
        } else {
            return `<span class="volume-amount">$${opp.min_volume.toFixed(0)}</span> <small class="slippage-info">(B:${buySlippage.toFixed(1)}% S:${sellSlippage.toFixed(1)}%)</small>`;
        }
    }
    return `<span class="exchange-count">${opp.exchanges_count} exchanges</span>`;
}

function formatMaxBuy(opp) {
    if (opp.volume_validated && opp.max_buy_usd !== undefined) {
        return `<span class="usd-amount">$${formatUSDAmount(opp.max_buy_usd)}</span>`;
    }
    return `<span class="no-data">-</span>`;
}

function formatMaxSell(opp) {
    if (opp.volume_validated && opp.max_sell_usd !== undefined) {
        return `<span class="usd-amount">$${formatUSDAmount(opp.max_sell_usd)}</span>`;
    }
    return `<span class="no-data">-</span>`;
}

function formatMaxProfit(opp) {
    if (opp.volume_validated && opp.max_profit_usd !== undefined) {
        return `<span class="profit-amount">$${opp.max_profit_usd.toFixed(2)}</span>`;
    }
    return `<span class="no-data">-</span>`;
}

function formatTokenAmount(amount) {
    // Smart formatting for token amounts
    if (amount === 0) {
        return '0';
    } else if (amount >= 1000000) {
        return (amount / 1000000).toFixed(2) + 'M';
    } else if (amount >= 1000) {
        return (amount / 1000).toFixed(2) + 'K';
    } else if (amount >= 1) {
        return amount.toFixed(2);
    } else if (amount >= 0.01) {
        return amount.toFixed(4);
    } else if (amount >= 0.0001) {
        return amount.toFixed(6);
    } else {
        // For very small amounts, use scientific notation
        return amount.toExponential(2);
    }
}

function formatUSDAmount(amount) {
    // Smart formatting for USD amounts
    if (amount === 0) {
        return '0';
    } else if (amount >= 1000000) {
        return (amount / 1000000).toFixed(2) + 'M';
    } else if (amount >= 1000) {
        return (amount / 1000).toFixed(2) + 'K';
    } else if (amount >= 1) {
        return amount.toFixed(2);
    } else {
        return amount.toFixed(2);
    }
}

function recalculateVolumesWithSlippage() {
    // Recalculate volumes for all opportunities with separate buy and sell slippage values
    const newBuySlippage = parseFloat(buySlippageSlider.value);
    const newSellSlippage = parseFloat(sellSlippageSlider.value);

    // Update each opportunity with new calculated volumes
    opportunities.forEach(opp => {
        if (opp.volume_validated) {
            // For now, we'll use a simple scaling approach
            // In a full implementation, we'd need the full orderbook data
            const buySlippageRatio = newBuySlippage / 3.0; // Scale from original 3% slippage
            const sellSlippageRatio = newSellSlippage / 3.0; // Scale from original 3% slippage

            // Scale the volumes (this is a simplified approach)
            if (opp.original_max_buy_usd === undefined) {
                opp.original_max_buy_usd = opp.max_buy_usd;
                opp.original_max_sell_usd = opp.max_sell_usd;
                opp.original_max_profit_usd = opp.max_profit_usd;
            }

            // Scale volumes independently for buy and sell sides
            opp.max_buy_usd = Math.round(opp.original_max_buy_usd * buySlippageRatio * 100) / 100;
            opp.max_sell_usd = Math.round(opp.original_max_sell_usd * sellSlippageRatio * 100) / 100;

            // Recalculate profit with new volumes
            const buy_tokens = opp.max_buy_usd / opp.buy_price;
            const sell_tokens = opp.max_sell_usd / opp.sell_price;
            const limiting_tokens = Math.min(buy_tokens, sell_tokens);
            opp.max_profit_usd = Math.round(limiting_tokens * (opp.sell_price - opp.buy_price) * 10000) / 10000;

            // Update slippage display (show the higher of the two for general reference)
            opp.current_buy_slippage = newBuySlippage;
            opp.current_sell_slippage = newSellSlippage;
            opp.current_slippage = Math.max(newBuySlippage, newSellSlippage);
        }
    });

    // Re-apply filters and re-render the table
    applyFilters();
}

function showLoading(show) {
    loadingIndicator.style.display = show ? 'block' : 'none';
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        ${message}
    `;

    // Add to page
    document.body.appendChild(notification);

    // Remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

// Add notification styles
const notificationStyles = `
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    z-index: 1000;
    animation: slideIn 0.3s ease;
}

.notification-info {
    background: linear-gradient(135deg, #3498db, #2980b9);
}

.notification-error {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
`;

// Add notification styles to head
const styleSheet = document.createElement('style');
styleSheet.textContent = notificationStyles;
document.head.appendChild(styleSheet);
