import ccxt.async_support as ccxt_async
import asyncio
import time
from collections import defaultdict

# Volume validation constants
MIN_VOLUME_USD = 10.0  # Minimum $10 USD volume requirement
MAX_SLIPPAGE_PERCENT = 3.0  # Maximum 3% slippage tolerance

def validate_orderbook_volume(orderbook_data, side='bid', min_volume_usd=MIN_VOLUME_USD, max_slippage_percent=MAX_SLIPPAGE_PERCENT):
    """
    Validate if orderbook has sufficient volume within slippage tolerance.

    Args:
        orderbook_data: Orderbook data with 'bids' and 'asks' arrays
        side: 'bid' or 'ask' to analyze
        min_volume_usd: Minimum USD volume required (default: $10)
        max_slippage_percent: Maximum slippage percentage (default: 3%)

    Returns:
        dict: {
            'valid': bool,
            'available_volume_usd': float,
            'effective_price': float,
            'slippage_percent': float,
            'orders_consumed': int
        }
    """
    if not orderbook_data or not orderbook_data.get(side + 's'):
        return {
            'valid': False,
            'available_volume_usd': 0.0,
            'effective_price': None,
            'slippage_percent': None,
            'orders_consumed': 0,
            'error': 'No orderbook data'
        }

    orders = orderbook_data[side + 's']  # 'bids' or 'asks'
    if not orders:
        return {
            'valid': False,
            'available_volume_usd': 0.0,
            'effective_price': None,
            'slippage_percent': None,
            'orders_consumed': 0,
            'error': 'No orders available'
        }

    # Get starting price (best bid/ask)
    start_price = orders[0][0]
    max_price_deviation = start_price * (max_slippage_percent / 100)

    # For bids: price should not go below (start_price - deviation)
    # For asks: price should not go above (start_price + deviation)
    if side == 'bid':
        price_limit = start_price - max_price_deviation
    else:  # ask
        price_limit = start_price + max_price_deviation

    cumulative_volume_usd = 0.0
    orders_consumed = 0
    total_cost = 0.0
    total_quantity = 0.0

    for price, quantity in orders:
        # Check if price is within slippage tolerance
        if side == 'bid' and price < price_limit:
            break  # Price too low for bids
        elif side == 'ask' and price > price_limit:
            break  # Price too high for asks

        # Calculate USD value of this order
        order_value_usd = price * quantity

        # Add this order to our aggregation
        cumulative_volume_usd += order_value_usd
        total_cost += order_value_usd
        total_quantity += quantity
        orders_consumed += 1

        # Check if we've reached minimum volume requirement
        if cumulative_volume_usd >= min_volume_usd:
            effective_price = total_cost / total_quantity if total_quantity > 0 else start_price
            slippage_percent = abs((effective_price - start_price) / start_price) * 100

            return {
                'valid': True,
                'available_volume_usd': cumulative_volume_usd,
                'effective_price': effective_price,
                'slippage_percent': slippage_percent,
                'orders_consumed': orders_consumed
            }

    # Didn't reach minimum volume within slippage tolerance
    effective_price = total_cost / total_quantity if total_quantity > 0 else start_price
    slippage_percent = abs((effective_price - start_price) / start_price) * 100 if total_quantity > 0 else 0

    return {
        'valid': False,
        'available_volume_usd': cumulative_volume_usd,
        'effective_price': effective_price,
        'slippage_percent': slippage_percent,
        'orders_consumed': orders_consumed,
        'error': f'Insufficient volume: ${cumulative_volume_usd:.2f} < ${min_volume_usd}'
    }

async def fetch_full_orderbook(exchange_name, symbol, semaphore, limit=50):
    """
    Fetch full orderbook with deeper data for volume analysis.

    Args:
        exchange_name: Name of the exchange
        symbol: Trading symbol (e.g., 'BTC/USDT')
        semaphore: Asyncio semaphore for rate limiting
        limit: Number of orderbook levels to fetch

    Returns:
        dict: Extended result with full orderbook data and volume validation
    """
    async with semaphore:
        try:
            exchange = exchange_pool[exchange_name]

            # Fetch deeper orderbook for volume analysis
            orderbook = await asyncio.wait_for(
                exchange.fetch_order_book(symbol, limit=limit),
                timeout=6.0
            )

            if not orderbook.get('bids') or not orderbook.get('asks'):
                return {
                    'exchange': exchange_name, 'symbol': symbol,
                    'bid': None, 'ask': None, 'orderbook': None,
                    'bid_volume_valid': False, 'ask_volume_valid': False,
                    'timestamp': time.time(), 'error': 'No orderbook data', 'method': 'full_orderbook'
                }

            # Get basic bid/ask prices
            best_bid = orderbook['bids'][0][0] if orderbook['bids'] else None
            best_ask = orderbook['asks'][0][0] if orderbook['asks'] else None

            # Validate volume for both sides
            bid_validation = validate_orderbook_volume(orderbook, 'bid')
            ask_validation = validate_orderbook_volume(orderbook, 'ask')

            return {
                'exchange': exchange_name, 'symbol': symbol,
                'bid': best_bid, 'ask': best_ask,
                'orderbook': orderbook,
                'bid_volume_valid': bid_validation['valid'],
                'ask_volume_valid': ask_validation['valid'],
                'bid_volume_data': bid_validation,
                'ask_volume_data': ask_validation,
                'timestamp': time.time(), 'error': None, 'method': 'full_orderbook'
            }

        except Exception as e:
            return {
                'exchange': exchange_name, 'symbol': symbol,
                'bid': None, 'ask': None, 'orderbook': None,
                'bid_volume_valid': False, 'ask_volume_valid': False,
                'timestamp': time.time(), 'error': str(e)[:50], 'method': 'full_orderbook_failed'
            }

# ALL exchanges as specified
SMART_EXCHANGES = [
    "ascendex",
    "bigone",
    "binance",
    "bitmart",
    "bitmex",
    "bitrue",
    "bitteam",
    "blockchaincom",
    "btcalpha",
    "btcturk",
    "cex",
    "coinbase",
    "coincatch",
    "coinsph",
    "cryptocom",
    "cryptomus",
    "digifinex",
    "hashkey",
    "hollaex",
    "kraken",
    "latoken",
    "lbank",
    "mexc",
    "myokx",
    "novadax",
    "oceanex",
    "p2b",
    "phemex",
    "poloniex",
    "probit",
    "tokocrypto",
    "tradeogre",
    "woo",
    "xt",
    "yobit"
]

# Exchange pool and market data
exchange_pool = {}
exchange_markets = {}

async def init_exchange_with_markets(exchange_name):
    """Initialize exchange and load its markets"""
    try:
        exchange_class = getattr(ccxt_async, exchange_name)
        exchange = exchange_class({
            'enableRateLimit': False,
            'timeout': 5000,
            'options': {'defaultType': 'spot'},
        })

        # Load markets to know which tokens exist
        markets = await exchange.load_markets()

        # Extract USDT pairs
        usdt_tokens = set()
        for symbol in markets:
            if '/USDT' in symbol and markets[symbol]['active']:
                token = symbol.split('/')[0]
                usdt_tokens.add(token)

        exchange_pool[exchange_name] = exchange
        exchange_markets[exchange_name] = usdt_tokens

        print(f"✓ {exchange_name}: {len(usdt_tokens)} USDT tokens")
        return True

    except Exception as e:
        print(f"✗ {exchange_name}: {e}")
        return False

async def init_smart_exchanges():
    """Initialize exchanges and build smart token mapping"""
    print("🧠 Smart Exchange Initialization...")

    tasks = [init_exchange_with_markets(ex) for ex in SMART_EXCHANGES]
    results = await asyncio.gather(*tasks, return_exceptions=True)

    successful = sum(1 for r in results if r is True)
    print(f"📊 {successful}/{len(SMART_EXCHANGES)} exchanges ready")

    # Build smart token-to-exchange mapping
    token_exchange_map = defaultdict(list)
    for exchange_name, tokens in exchange_markets.items():
        for token in tokens:
            token_exchange_map[token].append(exchange_name)

    # Filter to tokens on multiple exchanges
    multi_exchange_tokens = {
        token: exchanges for token, exchanges in token_exchange_map.items()
        if len(exchanges) >= 2
    }

    print(f"🎯 Found {len(multi_exchange_tokens)} tokens on multiple exchanges")

    return multi_exchange_tokens

async def fetch_smart(exchange_name, symbol, semaphore):
    """Smart fetch - only called for tokens that exist on the exchange"""
    async with semaphore:
        try:
            exchange = exchange_pool[exchange_name]

            # Try ticker first (fastest)
            try:
                ticker = await asyncio.wait_for(exchange.fetch_ticker(symbol), timeout=3.0)
                if ticker.get('bid') and ticker.get('ask'):
                    return {
                        'exchange': exchange_name, 'symbol': symbol,
                        'bid': ticker['bid'], 'ask': ticker['ask'],
                        'timestamp': time.time(), 'error': None, 'method': 'ticker'
                    }
            except:
                pass

            # Fallback to orderbook
            try:
                orderbook = await asyncio.wait_for(exchange.fetch_order_book(symbol, limit=1), timeout=4.0)
                if orderbook.get('bids') and orderbook.get('asks'):
                    return {
                        'exchange': exchange_name, 'symbol': symbol,
                        'bid': orderbook['bids'][0][0], 'ask': orderbook['asks'][0][0],
                        'timestamp': time.time(), 'error': None, 'method': 'orderbook'
                    }
            except Exception as e:
                return {
                    'exchange': exchange_name, 'symbol': symbol, 'bid': None, 'ask': None,
                    'timestamp': time.time(), 'error': str(e)[:50], 'method': 'failed'
                }

        except Exception as e:
            return {
                'exchange': exchange_name, 'symbol': symbol, 'bid': None, 'ask': None,
                'timestamp': time.time(), 'error': str(e)[:50], 'method': 'failed'
            }

        # Fallback return (should never reach here)
        return {
            'exchange': exchange_name, 'symbol': symbol, 'bid': None, 'ask': None,
            'timestamp': time.time(), 'error': 'No data available', 'method': 'no_data'
        }

async def retry_failed_requests(failed_requests, max_concurrent=30):
    """Retry failed requests with different strategy"""
    print(f"🔄 Retry strategy: {max_concurrent} concurrent requests with longer timeouts")

    semaphore = asyncio.Semaphore(max_concurrent)
    retry_tasks = []

    for exchange_name, symbol in failed_requests:
        task = fetch_smart_retry(exchange_name, symbol, semaphore)
        retry_tasks.append(task)

    # Execute retries
    retry_results = []
    completed = 0
    total_retries = len(retry_tasks)

    print("🔄 Retry progress: ", end="", flush=True)

    for coro in asyncio.as_completed(retry_tasks):
        try:
            result = await coro
            if result is not None:
                retry_results.append(result)
            completed += 1

            if completed % max(1, total_retries // 10) == 0:
                progress = (completed / total_retries) * 100
                print(f"{progress:.0f}%", end=" ", flush=True)
        except Exception as e:
            completed += 1
            retry_results.append({
                'exchange': 'unknown', 'symbol': 'unknown', 'bid': None, 'ask': None,
                'timestamp': time.time(), 'error': str(e)[:50], 'method': 'retry_failed'
            })

    print()

    # Count successful retries
    successful_retries = [r for r in retry_results
                         if r.get('error') is None and r.get('bid') and r.get('ask')]

    print(f"✅ Retry completed: {len(successful_retries)}/{total_retries} successful")

    return retry_results

async def fetch_smart_retry(exchange_name, symbol, semaphore):
    """Retry fetch with more aggressive timeouts and different approach"""
    async with semaphore:
        exchange = exchange_pool.get(exchange_name)
        if not exchange:
            return {
                'exchange': exchange_name, 'symbol': symbol, 'bid': None, 'ask': None,
                'timestamp': time.time(), 'error': 'Exchange not available', 'method': 'retry_unavailable'
            }

        # Strategy 1: Try orderbook first (more reliable for retries)
        try:
            orderbook = await asyncio.wait_for(exchange.fetch_order_book(symbol, limit=1), timeout=6.0)
            if orderbook.get('bids') and orderbook.get('asks'):
                return {
                    'exchange': exchange_name, 'symbol': symbol,
                    'bid': orderbook['bids'][0][0], 'ask': orderbook['asks'][0][0],
                    'timestamp': time.time(), 'error': None, 'method': 'retry_orderbook'
                }
        except:
            pass

        # Strategy 2: Try ticker with longer timeout
        try:
            ticker = await asyncio.wait_for(exchange.fetch_ticker(symbol), timeout=5.0)
            if ticker.get('bid') and ticker.get('ask'):
                return {
                    'exchange': exchange_name, 'symbol': symbol,
                    'bid': ticker['bid'], 'ask': ticker['ask'],
                    'timestamp': time.time(), 'error': None, 'method': 'retry_ticker'
                }
        except Exception as e:
            return {
                'exchange': exchange_name, 'symbol': symbol, 'bid': None, 'ask': None,
                'timestamp': time.time(), 'error': f"Retry failed: {str(e)[:30]}", 'method': 'retry_failed'
            }

        return {
            'exchange': exchange_name, 'symbol': symbol, 'bid': None, 'ask': None,
            'timestamp': time.time(), 'error': 'Retry: No data available', 'method': 'retry_no_data'
        }

async def smart_scan(token_exchange_map, max_concurrent=60, retry_failed=True, volume_validation=False):
    """Smart scan - only request tokens that exist on each exchange"""
    print(f"\n🧠 SMART SCAN: {max_concurrent} concurrent requests")
    print("🎯 Only requesting tokens that exist on each exchange")
    if volume_validation:
        print("💰 Volume validation enabled: $10 USD minimum with 3% slippage protection")
    if retry_failed:
        print("🔄 Will retry failed requests after initial scan")

    semaphore = asyncio.Semaphore(max_concurrent)
    tasks = []
    total_requests = 0
    request_info = []  # Store request info for retries

    # Only create requests for tokens that actually exist on each exchange
    for token, exchanges in token_exchange_map.items():
        symbol = f"{token}/USDT"
        for exchange_name in exchanges:
            # Double-check the token exists on this exchange
            if exchange_name in exchange_markets and token in exchange_markets[exchange_name]:
                if volume_validation:
                    task = fetch_full_orderbook(exchange_name, symbol, semaphore)
                else:
                    task = fetch_smart(exchange_name, symbol, semaphore)
                tasks.append(task)
                request_info.append((exchange_name, symbol))  # Store for retries
                total_requests += 1

    print(f"📊 Smart requests: {total_requests:,}")
    print(f"📈 Tokens: {len(token_exchange_map)}")
    print(f"🏢 Average exchanges per token: {total_requests/len(token_exchange_map):.1f}")

    start_time = time.time()

    # Execute with progress
    print("\n🔄 Initial scan progress: ", end="", flush=True)
    completed = 0
    results = []
    failed_requests = []  # Store failed requests for retry

    # Execute all tasks and collect results
    all_results = await asyncio.gather(*tasks, return_exceptions=True)

    # Process results and identify failures
    for i, result in enumerate(all_results):
        completed += 1

        if isinstance(result, Exception):
            # Exception occurred
            error_result = {
                'exchange': request_info[i][0] if i < len(request_info) else 'unknown',
                'symbol': request_info[i][1] if i < len(request_info) else 'unknown',
                'bid': None, 'ask': None,
                'timestamp': time.time(), 'error': str(result)[:50], 'method': 'exception'
            }
            results.append(error_result)
            if i < len(request_info):
                failed_requests.append(request_info[i])
        elif result is not None:
            results.append(result)
            # Check if this request failed and should be retried
            if result.get('error') is not None or not (result.get('bid') and result.get('ask')):
                if i < len(request_info):
                    failed_requests.append(request_info[i])

        if completed % max(1, total_requests // 20) == 0:
            progress = (completed / total_requests) * 100
            print(f"{progress:.0f}%", end=" ", flush=True)

    print()

    initial_duration = time.time() - start_time
    initial_successful = len([r for r in results if r.get('error') is None and r.get('bid') and r.get('ask')])

    print(f"📊 Initial scan: {initial_successful}/{total_requests} successful ({initial_successful/total_requests*100:.1f}%)")

    # Retry failed requests if enabled
    if retry_failed and failed_requests:
        print(f"\n🔄 Retrying {len(failed_requests)} failed requests...")
        retry_results = await retry_failed_requests(failed_requests, max_concurrent // 2)

        # Replace failed results with retry results where successful
        retry_dict = {(r['exchange'], r['symbol']): r for r in retry_results
                     if r.get('error') is None and r.get('bid') and r.get('ask')}

        # Update results with successful retries
        for i, result in enumerate(results):
            if result.get('error') is not None or not (result.get('bid') and result.get('ask')):
                key = (result.get('exchange', 'unknown'), result.get('symbol', 'unknown'))
                if key in retry_dict:
                    results[i] = retry_dict[key]

        # Add any new successful results from retries
        for retry_result in retry_results:
            key = (retry_result['exchange'], retry_result['symbol'])
            if key in retry_dict and retry_result not in results:
                results.append(retry_result)

    end_time = time.time()
    duration = end_time - start_time

    # Analyze results
    successful = []
    method_counts = defaultdict(int)
    exchange_success = defaultdict(int)
    exchange_total = defaultdict(int)

    for result in results:
        method_counts[result.get('method', 'unknown')] += 1
        exchange_total[result['exchange']] += 1

        if result['error'] is None and result['bid'] and result['ask']:
            successful.append(result)
            exchange_success[result['exchange']] += 1

    success_rate = len(successful) / total_requests * 100

    print(f"✅ SMART scan completed in {duration:.2f} seconds")
    print(f"📊 Final Success: {len(successful):,}/{total_requests:,} ({success_rate:.1f}%)")

    if retry_failed and failed_requests:
        retry_improvement = len(successful) - initial_successful
        print(f"🔄 Retry improvement: +{retry_improvement} successful requests")
        print(f"📈 Success rate improvement: {initial_successful/total_requests*100:.1f}% → {success_rate:.1f}%")

    print(f"🚀 Speed: {total_requests/duration:.0f} requests/second")

    print(f"\n📈 Method breakdown:")
    for method, count in sorted(method_counts.items(), key=lambda x: x[1], reverse=True):
        print(f"   {method}: {count:,}")

    print(f"\n🏢 Exchange performance:")
    for ex in sorted(exchange_success.keys()):
        if exchange_total[ex] > 0:
            rate = exchange_success[ex]/exchange_total[ex]*100
            print(f"   {ex}: {rate:.1f}% ({exchange_success[ex]}/{exchange_total[ex]})")

    return successful

def find_smart_arbitrage(results, volume_validation=False):
    """Find arbitrage with better filtering and optional volume validation"""
    symbol_data = defaultdict(list)
    for result in results:
        symbol_data[result['symbol']].append(result)

    opportunities = []

    for symbol, exchange_data in symbol_data.items():
        if len(exchange_data) < 2:
            continue

        # Find best bid and ask
        best_bid = None
        best_ask = None
        best_bid_exchange = None
        best_ask_exchange = None
        best_bid_data = None
        best_ask_data = None

        for data in exchange_data:
            # For volume validation, check if volume requirements are met
            if volume_validation:
                bid_valid = data.get('bid_volume_valid', False)
                ask_valid = data.get('ask_volume_valid', False)

                # Only consider bids/asks with valid volume
                if data['bid'] and bid_valid and (best_bid is None or data['bid'] > best_bid):
                    best_bid = data['bid']
                    best_bid_exchange = data['exchange']
                    best_bid_data = data

                if data['ask'] and ask_valid and (best_ask is None or data['ask'] < best_ask):
                    best_ask = data['ask']
                    best_ask_exchange = data['exchange']
                    best_ask_data = data
            else:
                # Original logic without volume validation
                if data['bid'] and (best_bid is None or data['bid'] > best_bid):
                    best_bid = data['bid']
                    best_bid_exchange = data['exchange']
                    best_bid_data = data

                if data['ask'] and (best_ask is None or data['ask'] < best_ask):
                    best_ask = data['ask']
                    best_ask_exchange = data['exchange']
                    best_ask_data = data

        if (best_bid and best_ask and best_bid > best_ask and
            best_bid_exchange != best_ask_exchange):

            profit_pct = ((best_bid - best_ask) / best_ask) * 100

            # Filter realistic opportunities (remove obvious data errors)
            if 0.01 <= profit_pct <= 15:  # Between 0.01% and 15%
                opportunity = {
                    'symbol': symbol,
                    'buy_exchange': best_ask_exchange,
                    'sell_exchange': best_bid_exchange,
                    'buy_price': best_ask,
                    'sell_price': best_bid,
                    'profit_percentage': profit_pct,
                    'profit_absolute': best_bid - best_ask,
                    'exchanges_count': len(exchange_data)
                }

                # Add volume validation data if available
                if volume_validation and best_bid_data and best_ask_data:
                    opportunity.update({
                        'buy_volume_data': best_ask_data.get('ask_volume_data', {}),
                        'sell_volume_data': best_bid_data.get('bid_volume_data', {}),
                        'volume_validated': True
                    })
                else:
                    opportunity['volume_validated'] = False

                opportunities.append(opportunity)

    return sorted(opportunities, key=lambda x: x['profit_percentage'], reverse=True)

def display_smart_results(opportunities, top_n=30, volume_validation=False):
    """Display smart results with optional volume validation info"""
    print(f"\n🎯 TOP {min(top_n, len(opportunities))} SMART ARBITRAGE OPPORTUNITIES")

    if volume_validation:
        print("💰 Volume Validated: Only showing opportunities with $10+ USD liquidity within 3% slippage")
        print("=" * 130)
    else:
        print("=" * 100)

    if not opportunities:
        print("No arbitrage opportunities found.")
        return

    if volume_validation:
        print(f"{'#':>2} {'SYMBOL':>12} {'BUY':>10} {'BUY PRICE':>12} {'SELL':>10} {'SELL PRICE':>12} {'PROFIT':>8} {'VOL':>6} {'SLIP':>6}")
        print("-" * 130)
    else:
        print(f"{'#':>2} {'SYMBOL':>12} {'BUY':>10} {'BUY PRICE':>12} {'SELL':>10} {'SELL PRICE':>12} {'PROFIT':>8} {'EXCH':>4}")
        print("-" * 100)

    for i, opp in enumerate(opportunities[:top_n], 1):
        base_info = (f"{i:>2} {opp['symbol']:>12} {opp['buy_exchange']:>10} "
                    f"${opp['buy_price']:>11.6f} {opp['sell_exchange']:>10} "
                    f"${opp['sell_price']:>11.6f} {opp['profit_percentage']:>7.3f}%")

        if volume_validation and opp.get('volume_validated'):
            # Show volume and slippage info
            buy_vol = opp.get('buy_volume_data', {}).get('available_volume_usd', 0)
            sell_vol = opp.get('sell_volume_data', {}).get('available_volume_usd', 0)
            buy_slip = opp.get('buy_volume_data', {}).get('slippage_percent', 0)
            sell_slip = opp.get('sell_volume_data', {}).get('slippage_percent', 0)

            print(f"{base_info} ${min(buy_vol, sell_vol):>5.0f} {max(buy_slip, sell_slip):>5.2f}%")
        else:
            print(f"{base_info} {opp['exchanges_count']:>3}")

    # Show volume validation summary if enabled
    if volume_validation:
        validated_count = sum(1 for opp in opportunities[:top_n] if opp.get('volume_validated'))
        print(f"\n💰 Volume validation summary:")
        print(f"   Opportunities with sufficient liquidity: {validated_count}/{min(top_n, len(opportunities))}")
        if validated_count > 0:
            avg_volumes = []
            max_slippages = []
            for opp in opportunities[:top_n]:
                if opp.get('volume_validated'):
                    buy_vol = opp.get('buy_volume_data', {}).get('available_volume_usd', 0)
                    sell_vol = opp.get('sell_volume_data', {}).get('available_volume_usd', 0)
                    buy_slip = opp.get('buy_volume_data', {}).get('slippage_percent', 0)
                    sell_slip = opp.get('sell_volume_data', {}).get('slippage_percent', 0)
                    avg_volumes.append(min(buy_vol, sell_vol))
                    max_slippages.append(max(buy_slip, sell_slip))

            if avg_volumes:
                print(f"   Average available volume: ${sum(avg_volumes)/len(avg_volumes):.2f}")
                print(f"   Average max slippage: {sum(max_slippages)/len(max_slippages):.3f}%")

def test_volume_validation():
    """Test volume validation with sample HNT/USDT orderbook data"""
    print("\n🧪 TESTING VOLUME VALIDATION")
    print("=" * 50)

    # Sample HNT/USDT orderbook data from the user
    sample_orderbook = {
        'symbol': 'HNT/USDT',
        'bids': [[4.096, 1.66], [4.094, 92.57], [4.091, 83.72], [4.087, 30.53], [4.084, 32.42], [4.083, 37.22], [4.078, 69.16], [4.075, 51.52], [4.073, 68.81], [4.071, 44.51], [4.068, 20.19], [4.064, 51.67], [4.062, 31.83], [4.059, 65.0], [4.056, 92.99], [4.055, 370.5], [4.051, 36.89], [4.048, 44.91], [4.047, 82.99], [4.043, 60.97], [4.039, 27.36], [4.037, 78.67], [4.034, 6.97], [4.032, 51.48], [4.028, 655.08], [4.025, 11.04], [4.022, 1155.9], [4.021, 74.33], [4.018, 55.53], [4.016, 10.93], [4.013, 84.55], [4.009, 94.13], [4.008, 66.23], [4.005, 47.58], [4.0, 48.55], [3.999, 7.12], [3.998, 654.87], [3.995, 74.99], [3.993, 51.6], [3.99, 73.89], [3.986, 37.72], [3.983, 29.66], [3.98, 48.3], [3.978, 543.2], [3.974, 5.27], [3.97, 98.39], [3.968, 73.41], [3.965, 47.57], [3.961, 6.46], [3.96, 88.42], [3.957, 27.37], [3.954, 73.84], [3.951, 41.51], [3.948, 322.34], [3.946, 90.25], [3.943, 75.75], [3.94, 88.05], [3.939, 38.67], [3.936, 74.81], [3.932, 20.26], [3.929, 8.06], [3.927, 92.8], [3.923, 4.37], [3.919, 59.04], [3.918, 26.87], [3.915, 560.25], [3.913, 14.1], [3.912, 46.22], [3.908, 6.27], [3.906, 45.42], [3.903, 56.76], [3.9, 21.53], [3.895, 96.95], [3.892, 100.83], [3.89, 19.48], [3.887, 49.78], [3.884, 342.03], [3.882, 20.53], [3.879, 66.82], [3.875, 61.9], [3.873, 75.36], [3.869, 88.1], [3.867, 63.33], [3.866, 1175.35], [3.863, 74.48], [3.861, 36.61], [3.858, 62.36], [3.855, 632.41], [3.852, 97.68], [3.848, 19.64], [3.847, 1628.71], [3.844, 44.98], [3.842, 101.88], [3.84, 658.38], [3.839, 30.6], [3.837, 50.89], [3.833, 75.76], [3.831, 97.75], [3.827, 68.35], [3.825, 74.06]],
        'asks': [[4.107, 11.13], [4.109, 27.49], [4.112, 23.47], [4.115, 70.92], [4.119, 19.35], [4.121, 27.3], [4.125, 80.4], [4.127, 17.45], [4.129, 44.05], [4.133, 3.63], [4.137, 20.47], [4.138, 6.82], [4.142, 3.31], [4.145, 93.61], [4.148, 69.75], [4.149, 431.05], [4.15, 58.09], [4.153, 95.17], [4.158, 42.06], [4.159, 63.16], [4.164, 24.16], [4.165, 82.5], [4.168, 34.76], [4.171, 29.98], [4.175, 90.01], [4.177, 53.61], [4.18, 47.88], [4.181, 1561.89], [4.183, 94.4], [4.188, 56.03], [4.19, 33.12], [4.191, 503.59], [4.194, 16.13], [4.197, 9.34], [4.198, 77.48], [4.203, 45.98], [4.204, 68.0], [4.208, 14.63], [4.212, 66.72], [4.214, 13.55], [4.218, 56.49], [4.22, 27.59], [4.224, 28.43], [4.225, 91.72], [4.228, 48.95], [4.232, 27.86], [4.235, 16.47], [4.237, 484.39], [4.241, 86.99], [4.244, 81.96], [4.247, 88.49], [4.25, 43.72], [4.254, 1333.84], [4.256, 269.89], [4.258, 77.63], [4.263, 53.75], [4.264, 68.05], [4.268, 17.78], [4.272, 2.48], [4.273, 61.96], [4.276, 48.83], [4.28, 37.47], [4.282, 17.16], [4.283, 306.09], [4.287, 44.36], [4.289, 74.71], [4.291, 22.78], [4.295, 65.61], [4.299, 51.07], [4.301, 45.22], [4.303, 66.05], [4.306, 24.65], [4.308, 363.83], [4.31, 50.85], [4.314, 1.9], [4.316, 16.84], [4.32, 88.94], [4.322, 433.17], [4.323, 86.97], [4.326, 33.35], [4.328, 59.82], [4.332, 6.58], [4.333, 59.08], [4.336, 84.52], [4.339, 23.34], [4.344, 51.37], [4.345, 63.73], [4.35, 37.76], [4.353, 57.63], [4.356, 27.16], [4.359, 28.43], [4.361, 461.48], [4.362, 31.25], [4.365, 7.65], [4.366, 712.86], [4.368, 44.54], [4.37, 512.04], [4.373, 31.04], [4.376, 15.66], [4.38, 48.4]],
        'timestamp': 1748365437814,
        'datetime': '2025-05-27T17:03:57.814Z',
        'nonce': 1747144146039
    }

    print(f"📊 Testing with {sample_orderbook['symbol']} orderbook")
    print(f"   Bids: {len(sample_orderbook['bids'])} levels")
    print(f"   Asks: {len(sample_orderbook['asks'])} levels")
    print(f"   Best bid: ${sample_orderbook['bids'][0][0]:.6f}")
    print(f"   Best ask: ${sample_orderbook['asks'][0][0]:.6f}")

    # Test bid side validation
    print(f"\n💰 BID SIDE ANALYSIS:")
    bid_result = validate_orderbook_volume(sample_orderbook, 'bid')
    print(f"   Valid: {bid_result['valid']}")
    print(f"   Available volume: ${bid_result['available_volume_usd']:.2f}")
    print(f"   Effective price: ${bid_result['effective_price']:.6f}")
    print(f"   Slippage: {bid_result['slippage_percent']:.3f}%")
    print(f"   Orders consumed: {bid_result['orders_consumed']}")
    if 'error' in bid_result:
        print(f"   Error: {bid_result['error']}")

    # Test ask side validation
    print(f"\n💰 ASK SIDE ANALYSIS:")
    ask_result = validate_orderbook_volume(sample_orderbook, 'ask')
    print(f"   Valid: {ask_result['valid']}")
    print(f"   Available volume: ${ask_result['available_volume_usd']:.2f}")
    print(f"   Effective price: ${ask_result['effective_price']:.6f}")
    print(f"   Slippage: {ask_result['slippage_percent']:.3f}%")
    print(f"   Orders consumed: {ask_result['orders_consumed']}")
    if 'error' in ask_result:
        print(f"   Error: {ask_result['error']}")

    # Summary
    print(f"\n📋 SUMMARY:")
    print(f"   Both sides valid: {bid_result['valid'] and ask_result['valid']}")
    if bid_result['valid'] and ask_result['valid']:
        min_volume = min(bid_result['available_volume_usd'], ask_result['available_volume_usd'])
        max_slippage = max(bid_result['slippage_percent'], ask_result['slippage_percent'])
        print(f"   Tradeable volume: ${min_volume:.2f}")
        print(f"   Max slippage: {max_slippage:.3f}%")
        print(f"   ✅ This orderbook meets volume requirements for arbitrage")
    else:
        print(f"   ❌ This orderbook does not meet volume requirements")

async def cleanup():
    """Close connections"""
    for exchange in exchange_pool.values():
        try:
            await exchange.close()
        except:
            pass

async def main():
    print("🧠 SMART ARBITRAGE SCANNER")
    print("=" * 50)
    print("✅ Only requests tokens that exist on each exchange")
    print("🎯 Dramatically improved success rates")
    print("⚡ Optimized for speed and accuracy")
    print("📊 Smart token-exchange mapping")
    print("💰 Volume validation with slippage protection")
    print()

    # Test volume validation with sample data first
    test_volume_validation()

    try:
        # Smart initialization with market data
        token_exchange_map = await init_smart_exchanges()

        if not token_exchange_map:
            print("❌ No tokens found")
            return

        # Calculate smart requests
        total_smart_requests = sum(len(exchanges) for exchanges in token_exchange_map.values())
        print(f"📊 Smart requests: {total_smart_requests:,}")
        print(f"💡 vs Naive approach: {len(token_exchange_map) * len(exchange_pool):,} requests")
        reduction = (1 - total_smart_requests / (len(token_exchange_map) * len(exchange_pool))) * 100
        print(f"🎯 Request reduction: {reduction:.1f}%")

        # Smart scan with volume validation option
        volume_validation = True  # Enable volume validation by default
        results = await smart_scan(token_exchange_map, max_concurrent=60, volume_validation=volume_validation)

        if results:
            print(f"\n🎉 Got {len(results):,} successful results!")

            opportunities = find_smart_arbitrage(results, volume_validation=volume_validation)

            if opportunities:
                display_smart_results(opportunities, top_n=40, volume_validation=volume_validation)

                print(f"\n📈 SMART SUMMARY:")
                print(f"   Exchanges used: {len(exchange_pool)}")
                print(f"   Tokens scanned: {len(token_exchange_map)}")
                print(f"   Successful requests: {len(results):,}")
                print(f"   Arbitrage opportunities: {len(opportunities)}")

                if opportunities:
                    profits = [o['profit_percentage'] for o in opportunities]
                    print(f"   Average profit: {sum(profits)/len(profits):.3f}%")
                    print(f"   Maximum profit: {max(profits):.3f}%")
                    print(f"   Median profit: {sorted(profits)[len(profits)//2]:.3f}%")

                    # Realistic profit ranges
                    very_high = len([p for p in profits if p > 5.0])
                    high = len([p for p in profits if 1.0 <= p <= 5.0])
                    medium = len([p for p in profits if 0.1 <= p < 1.0])
                    low = len([p for p in profits if p < 0.1])

                    print(f"   Very high profit (>5%): {very_high}")
                    print(f"   High profit (1-5%): {high}")
                    print(f"   Medium profit (0.1-1%): {medium}")
                    print(f"   Low profit (<0.1%): {low}")
            else:
                print("No arbitrage opportunities found")
        else:
            print("❌ No successful requests")

    except KeyboardInterrupt:
        print("\n⚠️  Interrupted")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await cleanup()

if __name__ == "__main__":
    asyncio.run(main())
