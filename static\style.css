/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    min-height: 100vh;
    color: #e0e0e0;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    background: rgba(45, 45, 45, 0.95);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.header h1 {
    color: #f0f0f0;
    font-size: 2rem;
    font-weight: 700;
}

.header h1 i {
    color: #64b5f6;
    margin-right: 10px;
}

.header-controls {
    display: flex;
    gap: 10px;
}

/* Buttons */
.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    font-size: 14px;
}

.btn-primary {
    background: linear-gradient(135deg, #64b5f6, #42a5f5);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(100, 181, 246, 0.4);
    background: linear-gradient(135deg, #42a5f5, #1e88e5);
}

.btn-secondary {
    background: linear-gradient(135deg, #616161, #424242);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(97, 97, 97, 0.4);
    background: linear-gradient(135deg, #424242, #303030);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Status Bar */
.status-bar {
    background: rgba(45, 45, 45, 0.95);
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.status-item {
    text-align: center;
}

.status-label {
    display: block;
    font-size: 12px;
    color: #b0b0b0;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-value {
    display: block;
    font-size: 18px;
    font-weight: 700;
    color: #f0f0f0;
    margin-top: 5px;
}

/* Controls */
.controls {
    background: rgba(45, 45, 45, 0.95);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.search-container {
    position: relative;
    flex: 1;
    min-width: 250px;
}

.search-container i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #b0b0b0;
}

.search-container input {
    width: 100%;
    padding: 12px 15px 12px 45px;
    border: 2px solid #555;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    background: #3a3a3a;
    color: #e0e0e0;
}

.search-container input:focus {
    outline: none;
    border-color: #64b5f6;
}

.search-container input::placeholder {
    color: #888;
}

.filter-container {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-container select {
    padding: 12px 15px;
    border: 2px solid #555;
    border-radius: 8px;
    background: #3a3a3a;
    color: #e0e0e0;
    font-size: 14px;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.filter-container select:focus {
    outline: none;
    border-color: #64b5f6;
}

/* Slippage controls wrapper */
.slippage-controls {
    display: flex;
    align-items: center;
    gap: 15px;
    position: relative;
}

/* Individual slippage container */
.slippage-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    min-width: 130px;
}

.slippage-container label {
    color: #e0e0e0;
    font-size: 14px;
    font-weight: 600;
    white-space: nowrap;
}

#buySlippageValue,
#sellSlippageValue {
    color: #64b5f6;
    font-weight: 700;
}

#buySlippageSlider,
#sellSlippageSlider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #555;
    outline: none;
    -webkit-appearance: none;
}

#buySlippageSlider::-webkit-slider-thumb,
#sellSlippageSlider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #64b5f6;
    cursor: pointer;
    border: 2px solid #fff;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

#buySlippageSlider::-moz-range-thumb,
#sellSlippageSlider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #64b5f6;
    cursor: pointer;
    border: 2px solid #fff;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.btn-reset {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    background: linear-gradient(135deg, #616161, #424242);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
    margin-left: 10px;
}

.btn-reset:hover {
    background: linear-gradient(135deg, #424242, #303030);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

/* Loading Indicator */
.loading-indicator {
    text-align: center;
    padding: 40px;
    background: rgba(45, 45, 45, 0.95);
    border-radius: 12px;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #e0e0e0;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #555;
    border-top: 4px solid #64b5f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Table */
.table-container {
    background: rgba(45, 45, 45, 0.95);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.opportunities-table {
    width: 100%;
    border-collapse: collapse;
}

.opportunities-table th {
    background: linear-gradient(135deg, #424242, #303030);
    color: #f0f0f0;
    padding: 15px 12px;
    text-align: left;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.opportunities-table td {
    padding: 15px 12px;
    border-bottom: 1px solid #555;
    font-size: 14px;
    color: #e0e0e0;
}

/* Price display styling */
.opportunities-table td:nth-child(3),
.opportunities-table td:nth-child(5) {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    white-space: nowrap;
    min-width: 120px;
}

/* Profit percentage column */
.opportunities-table td:nth-child(6) {
    font-family: 'Courier New', monospace;
    font-weight: 700;
    text-align: center;
}

/* Max Buy, Max Sell, Max Profit columns */
.opportunities-table td:nth-child(8),
.opportunities-table td:nth-child(9),
.opportunities-table td:nth-child(10) {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    text-align: center;
    white-space: nowrap;
    min-width: 100px;
}

/* Token name styling */
.opportunities-table td:nth-child(1) strong {
    color: #f0f0f0;
    font-size: 15px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Scientific notation styling */
.price-scientific {
    color: #e1bee7 !important;
    font-style: italic;
}



/* Exchange link styling */
.exchange-link {
    color: #64b5f6;
    text-decoration: none;
    font-weight: 600;
}

/* Profit styling - inline background around text only */
.profit-high {
    color: #ffffff;
    font-weight: 700;
    background: linear-gradient(135deg, #4caf50, #388e3c);
    padding: 4px 8px;
    border-radius: 6px;
    display: inline-block;
}

.profit-medium {
    color: #ffffff;
    font-weight: 600;
    background: linear-gradient(135deg, #66bb6a, #4caf50);
    padding: 4px 8px;
    border-radius: 6px;
    display: inline-block;
}

.profit-low {
    color: #ffffff;
    font-weight: 500;
    background: linear-gradient(135deg, #81c784, #66bb6a);
    padding: 4px 8px;
    border-radius: 6px;
    display: inline-block;
}



/* Volume styling */
.volume-amount {
    color: #ff9800;
    font-weight: 600;
}

.slippage-info {
    color: #b0b0b0;
    font-style: italic;
}

.exchange-count {
    color: #9e9e9e;
    font-weight: 500;
}

/* Token amount styling */
.token-amount {
    color: #81c784;
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

/* USD amount styling */
.usd-amount {
    color: #64b5f6;
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

/* Profit amount styling */
.profit-amount {
    color: #4caf50;
    font-weight: 700;
    font-family: 'Courier New', monospace;
}

/* No data styling */
.no-data {
    color: #666;
    font-style: italic;
}

/* Slippage modified indicator */
.slippage-modified {
    background: rgba(100, 181, 246, 0.1);
    border-left: 3px solid #64b5f6;
    position: relative;
}

.slippage-modified::before {
    content: "📊";
    position: absolute;
    left: -15px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 12px;
}

/* Action buttons */
.action-buttons {
    display: flex;
    gap: 5px;
}

.btn-small {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 6px;
    text-decoration: none;
    color: white;
    transition: all 0.3s ease;
}

.btn-buy {
    background: linear-gradient(135deg, #4caf50, #388e3c);
}

.btn-sell {
    background: linear-gradient(135deg, #f44336, #d32f2f);
}

.btn-small:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    background: rgba(45, 45, 45, 0.95);
    border-radius: 12px;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.empty-state i {
    font-size: 4rem;
    color: #666;
    margin-bottom: 20px;
}

.empty-state h3 {
    color: #f0f0f0;
    margin-bottom: 10px;
}

.empty-state p {
    color: #b0b0b0;
}

/* Footer */
.footer {
    text-align: center;
    padding: 20px;
    color: rgba(224, 224, 224, 0.8);
    font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .header-content {
        flex-direction: column;
        text-align: center;
    }

    .controls {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-container {
        justify-content: center;
    }

    .slippage-controls {
        flex-direction: column;
        gap: 10px;
        align-items: center;
    }

    .slippage-container {
        min-width: 120px;
    }

    .opportunities-table {
        font-size: 12px;
    }

    .opportunities-table th,
    .opportunities-table td {
        padding: 10px 8px;
    }

    .action-buttons {
        flex-direction: column;
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-success { color: #4caf50; }
.text-warning { color: #ff9800; }
.text-danger { color: #f44336; }
.text-muted { color: #b0b0b0; }
