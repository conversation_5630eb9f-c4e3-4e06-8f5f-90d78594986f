<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Arbitrage Scanner Dashboard</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1><i class="fas fa-chart-line"></i> Arbitrage Scanner Dashboard</h1>
                <div class="header-controls">
                    <button id="scanBtn" class="btn btn-primary">
                        <i class="fas fa-sync-alt"></i> Start Scan
                    </button>
                    <button id="refreshBtn" class="btn btn-secondary">
                        <i class="fas fa-refresh"></i> Refresh
                    </button>
                </div>
            </div>
        </header>

        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-item">
                <span class="status-label">Status:</span>
                <span id="scanStatus" class="status-value">Ready</span>
            </div>
            <div class="status-item">
                <span class="status-label">Last Scan:</span>
                <span id="lastScan" class="status-value">Never</span>
            </div>
            <div class="status-item">
                <span class="status-label">Opportunities:</span>
                <span id="opportunityCount" class="status-value">0</span>
            </div>
            <div class="status-item">
                <span class="status-label">Exchanges:</span>
                <span id="exchangeCount" class="status-value">0</span>
            </div>
        </div>

        <!-- Controls -->
        <div class="controls">
            <div class="search-container">
                <i class="fas fa-search"></i>
                <input type="text" id="searchInput" placeholder="Search by token symbol...">
            </div>

            <div class="filter-container">
                <select id="exchangeFilter">
                    <option value="">All Exchanges</option>
                </select>

                <select id="profitFilter">
                    <option value="">All Profits</option>
                    <option value="high">High (>5%)</option>
                    <option value="medium">Medium (1-5%)</option>
                    <option value="low">Low (<1%)</option>
                </select>

                <select id="sortBy">
                    <option value="profit_desc">Profit % (High to Low)</option>
                    <option value="profit_asc">Profit % (Low to High)</option>
                    <option value="symbol_asc">Symbol (A-Z)</option>
                    <option value="volume_desc">Volume (High to Low)</option>
                    <option value="max_profit_desc">Max Profit (High to Low)</option>
                    <option value="max_buy_desc">Max Buy (High to Low)</option>
                    <option value="max_sell_desc">Max Sell (High to Low)</option>
                </select>

                <div class="slippage-controls">
                    <div class="slippage-container">
                        <label for="buySlippageSlider">Buy Slippage: <span id="buySlippageValue">3.0</span>%</label>
                        <input type="range" id="buySlippageSlider" min="0.5" max="10" step="0.1" value="3.0">
                    </div>
                    <div class="slippage-container">
                        <label for="sellSlippageSlider">Sell Slippage: <span id="sellSlippageValue">3.0</span>%</label>
                        <input type="range" id="sellSlippageSlider" min="0.5" max="10" step="0.1" value="3.0">
                    </div>
                    <button id="resetSlippage" class="btn-reset" title="Reset both to 3%">
                        <i class="fas fa-undo"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Loading Indicator -->
        <div id="loadingIndicator" class="loading-indicator" style="display: none;">
            <div class="spinner"></div>
            <span>Scanning exchanges for arbitrage opportunities...</span>
        </div>

        <!-- Opportunities Table -->
        <div class="table-container">
            <table id="opportunitiesTable" class="opportunities-table">
                <thead>
                    <tr>
                        <th>Token</th>
                        <th>Buy Exchange</th>
                        <th>Buy Price</th>
                        <th>Sell Exchange</th>
                        <th>Sell Price</th>
                        <th>Profit %</th>
                        <th>Volume</th>
                        <th>Max Buy</th>
                        <th>Max Sell</th>
                        <th>Max Profit</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="opportunitiesBody">
                    <!-- Opportunities will be populated here -->
                </tbody>
            </table>
        </div>

        <!-- Empty State -->
        <div id="emptyState" class="empty-state" style="display: none;">
            <i class="fas fa-search"></i>
            <h3>No Arbitrage Opportunities Found</h3>
            <p>Click "Start Scan" to search for arbitrage opportunities across exchanges.</p>
        </div>

        <!-- Footer -->
        <footer class="footer">
            <p>Arbitrage Scanner Dashboard - Real-time cryptocurrency arbitrage opportunities</p>
            <p><small>Data refreshed automatically. Always verify prices on exchange before trading.</small></p>
        </footer>
    </div>

    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>
